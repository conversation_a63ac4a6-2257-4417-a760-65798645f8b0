"""
MeowAgent FastAPI 服务器 (api.py)

该模块负责搭建和运行一个基于 FastAPI 的应用服务器，为 MeowAgent 框架提供核心的 API 接口。
它使得客户端（例如 Web 前端、CLI 工具或其他服务）能够与 MeowAgent 的智能体 (Agent) 进行交互，
管理对话历史，并获取智能体的状态信息。

主要功能和逻辑包括：

1.  **FastAPI 应用设置**:
    *   初始化 FastAPI 应用实例 (`app`)。
    *   配置 CORS (Cross-Origin Resource Sharing) 中间件，允许跨域请求。
    *   定义 API 版本前缀 (例如 `/api/v1`)，便于版本管理。
    *   实现应用生命周期管理 (`lifespan` 函数)：
        *   在应用启动时，通过 `AgentManager` 预热一个或多个 Agent 实例，以减少首次用户请求时的延迟。
          这包括模型加载、Tokenizer 初始化以及库的初步加载。
        *   在应用关闭时，优雅地关闭 `AgentManager` 并释放相关资源。

2.  **WebSocket API (`/ws` 端点)**:
    *   提供一个 WebSocket 端点，用于与客户端进行实时、双向的流式通信。
    *   **核心职责**:
        *   接收客户端通过结构化 `WebSocketMessageFrame` 发送的指令和消息。
        *   为每个连接的客户端（基于 `conversation_id`）获取或创建对应的 `Agent` 实例。
            *   实现了 Agent 克隆机制：对于新的会话，如果存在预热的 Agent，会尝试克隆该 Agent 以加速初始化。
            *   管理 `Agent` 与 `FastAPIWebSocketConnection` 之间的关联。
        *   处理不同类型的客户端请求：
            *   `user_input`: 将用户输入的消息转发给 `Agent` 处理，并流式返回 Agent 的响应。
            *   `request_initial_state`: 发送 Agent 的初始状态给客户端，包括已加载的库信息、工作流状态等。
            *   `request_conversation_history`: 获取并发送当前对话的完整历史记录。
            *   `request_libraries_status`: 获取并发送 Agent 中所有库的当前状态和详细信息。
            *   `request_workflow_status`: 获取并发送与 `WorkflowLibrary` 相关的工作流状态。
            *   `disconnect_request`: 处理客户端主动断开连接的请求。
        *   向客户端发送结构化的响应消息，包括 Agent 生成的内容、错误信息、状态更新等。
        *   处理 WebSocket 连接的建立、断开以及异常情况。

3.  **HTTP REST API (前缀 `/api/v1`)**:
    *   提供一组 RESTful HTTP 端点，用于对对话和消息进行管理和操作。
    *   **主要端点**:
        *   **对话管理**:
            *   `GET /conversations`: 列出所有已知的对话ID (包括持久化的和活跃的)。
            *   `DELETE /conversations/{conversation_id}`: 删除指定的对话，包括其历史记录和活跃的 Agent 实例。
        *   **消息管理**:
            *   `GET /conversations/{conversation_id}/messages`: 获取指定对话的完整消息历史。
            *   `GET /conversations/{conversation_id}/messages/formatted`: 获取OpenAI格式化（例如，经过渲染和裁剪）的消息列表。
            *   `DELETE /conversations/{conversation_id}/messages/{message_id}`: 删除对话中的特定消息。
            *   `PUT /conversations/{conversation_id}/messages/reorder`: 根据提供的ID列表重新排序对话中的消息。
            *   `PUT /conversations/{conversation_id}/messages/{message_id}`: 更新指定消息的内容、摘要或其他可编辑字段。
    *   **依赖注入**: 使用 FastAPI 的 `Depends` 机制来获取与特定 `conversation_id` 关联的 `Agent` 实例，确保 HTTP 请求能够操作正确的 Agent 和对话数据。如果 Agent 不存在，会尝试创建一个新的。

4.  **数据模型 (Pydantic)**:
    *   定义了一系列 Pydantic 模型 (如 `ApiMessage`, `ConversationHistoryResponse`, `WebSocketMessageFrame`, `UpdateMessagePayload`, `FormattedApiMessage` 等) 用于：
        *   请求体验证和序列化。
        *   响应体格式化和文档生成。
        *   确保 WebSocket 通信和 HTTP API 的数据结构清晰、一致。

5.  **核心模块集成**:
    *   与 `AgentManager`: 负责管理所有 `Agent` 实例的生命周期，包括创建、获取、克隆和关闭。
    *   与 `Agent`: 每个客户端连接（通过 `conversation_id` 区分）都与一个 `Agent` 实例交互，`Agent` 负责处理业务逻辑、模型调用、工具使用等。
    *   与 `Conversation` 和 `History`: 通过 `Agent` 间接访问，用于管理对话状态和消息持久化。
    *   与 `FastAPIWebSocketConnection`: `connection.py` 中定义的适配器类，用于将 FastAPI 的 WebSocket 对象包装成 `Agent` 可以使用的输出连接。
    *   与 `database.manager`: 通过 `db_manager_provider` 访问数据库，例如在列出所有对话时查询持久化的历史记录。

6.  **辅助与工具**:
    *   包含辅助函数，如 `_convert_message_to_api_message` (将内部 `Message` 对象转换为 API 响应模型) 和 `send_websocket_message` (封装 WebSocket 消息发送逻辑)。
    *   使用 `log.py` 模块进行结构化日志记录。

该模块是 MeowAgent 框架与外部世界交互的主要网关，提供了强大而灵活的接口来驱动和管理智能体的行为。
"""

import asyncio
import json
import traceback
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List, cast
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, Path, Body, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from motor.motor_asyncio import AsyncIOMotorDatabase, AsyncIOMotorCollection

from log import logger
from agent_manager import AgentManager
from agent import Agent
from connection import FastAPIWebSocketConnection
from conversation import Conversation
from message import SystemMessage, Message, UserMessage # 导入Message模块用于Message.from_dict
# 导入History用于类型提示（如有必要），但更倾向于使用agent接口
from history import History # 通过agent.conversation.history间接使用
import config
# 导入 WorkflowLibrary 以进行类型检查
from libraries.workflow import WorkflowLibrary
# 导入 dumps 用于序列化时间戳
from utils import dumps
from database.manager import db_manager_provider


# 定义API模型
class ApiMessage(BaseModel):
    id: Optional[str] = None
    object: Optional[str] = None # 对应 Message.__class__.__name__
    role: str
    content: Optional[Any] = None
    timestamp: Optional[str] = None # ISO格式字符串
    summary: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    display_style: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "id": "msg_123",
                "object": "UserMessage",
                "role": "user",
                "content": "Hello, how are you?",
                "timestamp": "2023-06-15T12:34:56.789012",
                "summary": "用户问候",
                "display_style": "user"
            }
        }

class ConversationHistoryResponse(BaseModel):
    history: List[ApiMessage]

class InitialStatePayload(BaseModel):
    libraries: List[Dict[str, Any]]
    workflow_status: Optional[Dict[str, Any]] = None

class WebSocketMessageFrame(BaseModel):
    type: str
    payload: Optional[Any] = None
    content: Optional[Any] = None
    conversation_id: Optional[str] = None
    timestamp: Optional[str] = None
    stream_mode: Optional[bool] = None
    workflow_name: Optional[str] = None

# HTTP API的Pydantic模型
class ReorderMessagesRequest(BaseModel):
    ordered_message_ids: List[str] = Field(..., example=["msg_id_3", "msg_id_1", "msg_id_2"])

class ListConversationsResponse(BaseModel):
    conversation_ids: List[str] = Field(..., example=["conv_123", "conv_456"])

class UpdateMessagePayload(BaseModel):
    # 定义允许更新的字段
    # 使用通用字典以保持灵活性，History.update_message将进行验证
    content: Optional[Any] = Field(None, description="消息的新内容，结构取决于消息类型")
    summary: Optional[str] = Field(None, description="消息的新摘要")
    display_style: Optional[str] = Field(None, description="消息的新显示样式")
    # tool_calls和tool_call_id不常直接更新，但如有需要可以包含
    # tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="助手消息的新tool_calls")
    # tool_call_id: Optional[str] = Field(None, description="工具消息的新tool_call_id")

    class Config:
        json_schema_extra = {
            "example": {
                "content": "这是消息的更新文本内容",
                "summary": "更新的摘要"
            }
        }

# 新增：用于OpenAI格式化消息的Pydantic模型
class FormattedApiMessage(BaseModel):
    role: str
    content: Optional[Any] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None # OpenAI格式有时包含name字段，例如当role="tool"时

    class Config:
        json_schema_extra = {
            "example": {
                "role": "assistant",
                "content": "Hello!",
                "tool_calls": [{
                    "id": "call_123",
                    "type": "function",
                    "function": {"name": "get_weather", "arguments": "{\\\"location\\\": \\\"Boston\\\"}"}
                }]
            }
        }

class FormattedConversationHistoryResponse(BaseModel):
    history: List[FormattedApiMessage]


@asynccontextmanager
async def lifespan(app: FastAPI):
    agent_manager = await AgentManager.get_instance()
    
    logger.info("FastAPI生命周期：正在尝试预热Agent...")
    warmup_conversation_id = "_api_warmup"
    try:
        # 为预热创建一个共享的连接对象
        from connection import DummyConnection
        dummy_connection = DummyConnection()
        conversation = Conversation(warmup_conversation_id)
        
        # 直接使用AgentManager创建预热Agent
        warmup_agent = await agent_manager.create_agent(warmup_conversation_id, conversation, dummy_connection)
        logger.info(f"对话ID {warmup_conversation_id} 的Agent预热完成。")
        
        # 额外预热：执行一次token计数，确保tokenizer完全初始化
        dummy_messages = [{"role": "system", "content": "预热系统，测试tokenizer"}, 
                         {"role": "user", "content": "测试消息"}]
        token_count = await conversation.count_tokens(warmup_agent.model)
        logger.info(f"预热token计数完成: {token_count} tokens")
        
        # 预热工作流和库的初始化
        for lib_name, library in warmup_agent.libraries.items():
            if hasattr(library, 'get_prompt') and callable(library.get_prompt):
                _ = library.get_prompt()
                logger.info(f"预热库 {lib_name} 的prompt获取")
        
        logger.info(f"Agent预热完成，所有资源已初始化")
    except Exception as e:
        logger.error(f"对话ID {warmup_conversation_id} 的Agent预热失败: {e}")
    
    yield
    
    logger.info("FastAPI生命周期：正在关闭。关闭AgentManager...")
    await agent_manager.close()
    logger.info("FastAPI生命周期：AgentManager已关闭。")

app = FastAPI(lifespan=lifespan)


# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], # 在生产环境中应配置为特定的前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API前缀
API_PREFIX = "/api/v1"


# 依赖项：获取agent实例的统一接口
async def _get_agent_for_http_routes(conversation_id: str, workflow_name: Optional[str] = None) -> Agent:
    agent_manager = await AgentManager.get_instance()
    agent = agent_manager.get_agent(conversation_id)
    if not agent:
        logger.info(f"对话ID {conversation_id} 没有现有的Agent (HTTP请求)，正在创建新的。")
        try:
            conversation = Conversation(conversation_id)
            # 构造agent配置，包括指定的工作流
            agent_config = {}
            if workflow_name:
                agent_config['default_workflow_name'] = workflow_name
                logger.info(f"HTTP API将使用指定的工作流: {workflow_name}")
            # 对于HTTP路由，我们不传递 output_connection
            agent = await agent_manager.create_agent(conversation_id, conversation, None, agent_config)
            logger.info(f"成功为对话ID {conversation_id} (HTTP请求) 创建了新Agent")
        except Exception as e:
            logger.error(f"为HTTP请求创建Agent失败 (conv_id: {conversation_id}): {e}", show_traceback=True)
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"创建Agent失败: {str(e)}")
    return agent

# 辅助函数：为WebSocket获取或更新agent实例的连接
async def _get_or_create_agent_for_websocket(
    conversation_id: str, 
    ws_connection: FastAPIWebSocketConnection,
    workflow_name: Optional[str] = None
) -> Agent:
    agent_manager = await AgentManager.get_instance()
    
    # 如果是预热Agent且请求的是新会话，则使用新会话ID
    if conversation_id == "_api_warmup":
        conversation_id = f"ws_{uuid.uuid4().hex[:8]}"  # 生成新的会话ID
        logger.info(f"WebSocket客户端请求了预热Agent，分配新会话ID: {conversation_id}")
    
    agent = agent_manager.get_agent(conversation_id)
    if not agent:
        # 构造agent配置，包括指定的工作流
        agent_config = {}
        if workflow_name:
            agent_config['default_workflow_name'] = workflow_name
            logger.info(f"WebSocket API将使用指定的工作流: {workflow_name}")
        
        # 尝试获取预热Agent
        warmup_agent = agent_manager.get_agent("_api_warmup")
        
        if warmup_agent and not workflow_name:
            # 只有在没有指定特定工作流时才克隆预热Agent
            # 如果指定了工作流，直接创建新Agent
            logger.info(f"为对话ID {conversation_id} 克隆预热Agent")
            try:
                start_time = asyncio.get_event_loop().time()
                agent = await warmup_agent.clone_for_new_conversation(conversation_id, ws_connection)
                elapsed = asyncio.get_event_loop().time() - start_time
                logger.info(f"成功为对话ID {conversation_id} 克隆预热Agent并设置了连接 (耗时: {elapsed:.3f}s)")
                return agent
            except Exception as e_clone:
                logger.error(f"克隆预热Agent失败 (conv_id: {conversation_id}): {e_clone}", show_traceback=True)
                # 如果克隆失败，下面会尝试常规创建
        
        logger.info(f"对话ID {conversation_id} 没有现有的Agent (WS请求)，正在创建新的。")
        try:
            start_time = asyncio.get_event_loop().time()
            conversation = Conversation(conversation_id)
            agent = await agent_manager.create_agent(conversation_id, conversation, ws_connection, agent_config)
            elapsed = asyncio.get_event_loop().time() - start_time
            logger.info(f"成功为对话ID {conversation_id} (WS请求) 创建了新Agent并设置了连接 (耗时: {elapsed:.3f}s)")
        except Exception as e:
            logger.error(f"为WS请求创建Agent失败 (conv_id: {conversation_id}): {e}", show_traceback=True)
            raise  # 重新抛出以便WebSocket端点可以捕获并发送错误消息
    elif agent.output_connection != ws_connection:
        logger.info(f"正在更新对话ID {conversation_id} 现有Agent的输出连接 (WS请求)")
        await agent.update_output_connection(ws_connection)
        
    return agent


# --- 用于对话历史的HTTP API端点 ---

def _convert_message_to_api_message(msg: Message) -> ApiMessage:
    """将内部Message对象转换为ApiMessage Pydantic模型的辅助函数"""
    msg_dict = msg.to_dict() # 这已经设置了'object'和'role'
    return ApiMessage(**msg_dict)

@app.get(f"{API_PREFIX}/conversations/{{conversation_id}}/messages", response_model=ConversationHistoryResponse)
async def get_conversation_messages(
    conversation_id: str = Path(..., description="对话的ID"),
    agent: Agent = Depends(_get_agent_for_http_routes)
):
    """
    获取指定对话的所有消息历史记录。
    """
    try:
        messages: List[Message] = await agent.conversation.history.get_messages()
        api_messages: List[ApiMessage] = [_convert_message_to_api_message(msg) for msg in messages]
        return ConversationHistoryResponse(history=api_messages)
    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 消息时出错: {e}", show_traceback=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取消息失败: {str(e)}")

@app.get(f"{API_PREFIX}/conversations/{{conversation_id}}/messages/formatted", response_model=FormattedConversationHistoryResponse)
async def get_formatted_conversation_messages(
    conversation_id: str = Path(..., description="对话的ID"),
    agent: Agent = Depends(_get_agent_for_http_routes)
):
    """
    获取指定对话的OpenAI格式化消息列表。
    消息将通过Conversation的renderer_pipeline进行处理。
    """
    try:
        # 调用conversation的get_formatted_messages方法
        # exclude_system=False 表示包含系统消息
        # model=agent.model 传递模型给渲染流程（例如，用于TrimRenderer的token计数）
        formatted_messages: List[Dict[str, Any]] = await agent.conversation.get_formatted_messages(
            exclude_system=False, 
            model=agent.model
        )
        #直接使用formatted_messages，因为它们已经是 List[Dict[str, Any]]
        #FormattedApiMessage模型会根据这些字典进行验证和转换
        return FormattedConversationHistoryResponse(history=formatted_messages)
    except Exception as e:
        logger.error(f"获取对话 {conversation_id} 的格式化消息时出错: {e}", show_traceback=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取格式化消息失败: {str(e)}")

@app.delete(f"{API_PREFIX}/conversations/{{conversation_id}}/messages/{{message_id}}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_conversation_message(
    conversation_id: str = Path(..., description="对话的ID"),
    message_id: str = Path(..., description="要删除的消息ID"),
    agent: Agent = Depends(_get_agent_for_http_routes)
):
    """
    删除对话中的指定消息。
    """
    try:
        success = await agent.conversation.history.delete_message(message_id)
        if not success:
            # History.delete_message在未找到时记录警告，但API应返回404
            existing_message = await agent.conversation.history.get_message_by_id(message_id)
            if not existing_message:
                 raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="消息未找到")
            else: # 消息存在但由于其他原因删除操作失败
                 raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除消息操作失败")
        return # 成功时返回HTTP 204 No Content
    except HTTPException: # 重新抛出HTTPExceptions（如404）
        raise
    except Exception as e:
        logger.error(f"删除消息 {message_id} 从对话 {conversation_id} 时出错: {e}", show_traceback=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"删除消息失败: {str(e)}")

@app.put(f"{API_PREFIX}/conversations/{{conversation_id}}/messages/reorder", status_code=status.HTTP_200_OK)
async def reorder_conversation_messages(
    conversation_id: str = Path(..., description="对话的ID"),
    request_body: ReorderMessagesRequest = Body(...),
    agent: Agent = Depends(_get_agent_for_http_routes)
):
    """
    根据提供的ID列表重新排序对话中的消息。
    """
    try:
        success = await agent.conversation.history.reorder_messages(request_body.ordered_message_ids)
        if not success:
            # reorder_messages记录详情，难以在此确定具体是404还是500
            # 简化处理：如果重新排序失败，则是服务器端问题或请求数据错误（如无效ID）
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="消息重新排序失败")
        # 可选返回重新排序的列表或仅返回成功
        return {"message": "消息已成功重新排序"}
    except Exception as e:
        logger.error(f"重新排序对话 {conversation_id} 消息时出错: {e}", show_traceback=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"消息重新排序失败: {str(e)}")

@app.put(f"{API_PREFIX}/conversations/{{conversation_id}}/messages/{{message_id}}", response_model=ApiMessage)
async def update_conversation_message(
    conversation_id: str = Path(..., description="对话的ID"),
    message_id: str = Path(..., description="要更新的消息ID"),
    payload: UpdateMessagePayload = Body(...),
    agent: Agent = Depends(_get_agent_for_http_routes)
):
    """
    更新对话中指定消息的内容。
    仅更新在请求体中提供的字段。
    """
    try:
        update_data = payload.model_dump(exclude_unset=True) # 仅包含已设置的字段
        if not update_data:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="请求体中没有提供要更新的字段")

        success = await agent.conversation.history.update_message(message_id, update_data)
        
        if not success:
            # 检查消息是否存在以区分404和其他错误
            existing_message_check = await agent.conversation.history.get_message_by_id(message_id)
            if not existing_message_check:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="消息未找到")
            # 如果消息存在但更新失败（例如没有字段实际更改，或数据库错误）
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新消息失败")


        updated_message = await agent.conversation.history.get_message_by_id(message_id)
        if not updated_message:
            # 理论上这种情况应该已由success检查或上面的404捕获，但作为安全措施：
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="更新后消息未找到")
        
        return _convert_message_to_api_message(updated_message)

    except HTTPException: # 重新抛出HTTPExceptions（如400、404）
        raise
    except Exception as e:
        logger.error(f"更新消息 {message_id} (对话 {conversation_id}) 时出错: {e}", show_traceback=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"更新消息失败: {str(e)}")

@app.get(f"{API_PREFIX}/conversations", response_model=ListConversationsResponse)
async def list_conversations():
    """
    获取所有持久化的对话ID列表。
    """
    try:
        manager = await db_manager_provider.get_manager()
        history_collection = await manager.get_history_messages_collection()

        if history_collection is None:
            logger.error("无法获取历史记录集合。")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器配置错误，无法获取对话列表。")

        # 从数据库获取所有不同的 conversation_id
        persisted_conversation_ids: List[str] = await history_collection.distinct("conversation_id")
        
        # 获取当前活跃但可能还没有历史记录的agent的ID
        agent_manager = await AgentManager.get_instance()
        active_agent_ids = agent_manager.list_conversation_ids()
        
        # 合并并去重
        all_conversation_ids = list(set(persisted_conversation_ids + active_agent_ids))
        
        # 过滤掉预热的对话ID
        all_conversation_ids = [cid for cid in all_conversation_ids if cid != "_api_warmup"]

        # 按ID字符串反向排序（通常意味着较新的在前，如果ID有某种顺序性的话）
        all_conversation_ids.sort(reverse=True)

        return ListConversationsResponse(conversation_ids=all_conversation_ids)
    except Exception as e:
        logger.error(f"获取对话列表时出错: {e}", show_traceback=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取对话列表失败: {str(e)}")

@app.delete(f"{API_PREFIX}/conversations/{{conversation_id}}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_conversation(
    conversation_id: str = Path(..., description="要删除的对话ID")
):
    """
    删除指定的对话，包括其所有消息历史和活跃的Agent实例。
    """
    try:
        agent_manager = await AgentManager.get_instance()
        success = await agent_manager.delete_conversation(conversation_id)
        if not success:
            # delete_conversation 内部会记录具体原因 (e.g., agent not found but history delete failed)
            existing_history = History(conversation_id)
            messages = await existing_history.get_messages() # 检查历史记录
            agent = agent_manager.get_agent(conversation_id) # 检查活跃 agent
            if not messages and not agent:
                 raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="对话未找到或删除失败")
            
            # 如果仍有残留或删除操作本身失败
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除对话操作失败")
        return # 成功时返回HTTP 204 No Content
    except HTTPException: # 重新抛出HTTPExceptions（如404、500）
        raise
    except Exception as e:
        logger.error(f"删除对话 {conversation_id} 时出错: {e}", show_traceback=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"删除对话失败: {str(e)}")


# --- WebSocket逻辑 ---

# 发送WebSocket消息的辅助函数
async def send_websocket_message(websocket: WebSocket, msg_type: str, payload: Optional[Any] = None, content: Optional[Any] = None, conv_id: Optional[str] = None):
    """发送结构化消息到WebSocket的辅助函数"""
    try:
        frame = WebSocketMessageFrame(
            type=msg_type,
            payload=payload,
            content=content,
            conversation_id=conv_id,
            timestamp=datetime.now().isoformat()
        )
        await websocket.send_text(frame.model_dump_json(exclude_none=True))
    except WebSocketDisconnect:
        logger.warning(f"尝试发送 {msg_type} 给对话 {conv_id} 时WebSocket已断开")
    except Exception as e:
        logger.error(f"发送WebSocket消息时出错 ({msg_type} 给对话 {conv_id}): {e}")

async def send_initial_state(agent: Agent, websocket: WebSocket, conversation_id: str):
    """发送初始状态给客户端。"""
    try:
        libraries_details = await agent.get_all_libraries_details()
        
        workflow_status_payload = None
        workflow_lib = next((lib for lib in agent.libraries.values() if isinstance(lib, WorkflowLibrary)), None)
        if workflow_lib:
            workflow_status_payload = await workflow_lib.get_library_details()
            
        initial_payload_obj = InitialStatePayload(
            libraries=libraries_details,
            workflow_status=workflow_status_payload
        )
        await send_websocket_message(websocket, "initial_state_response", payload=initial_payload_obj.model_dump(exclude_none=True), conv_id=conversation_id)
        logger.info(f"已向 {conversation_id} 发送初始状态")
    except Exception as e:
        logger.error(f"发送初始状态给 {conversation_id} 时出错: {e}", show_traceback=True)

@app.websocket("/ws") # 保持WebSocket在根路径或确保前缀一致性
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点处理"""
    await websocket.accept()
    
    agent_manager = await AgentManager.get_instance()
    current_agent: Optional[Agent] = None
    current_conversation_id: Optional[str] = None
    ws_connection = FastAPIWebSocketConnection(websocket)

    try:
        while True:
            raw_message = await websocket.receive_text()
            try:
                incoming_frame = WebSocketMessageFrame.model_validate_json(raw_message)
            except Exception as e_parse:
                logger.error(f"WebSocket接收文本解析JSON失败: '{raw_message}', 错误: {e_parse}")
                await send_websocket_message(websocket, "error", content=f"无效的消息格式: {e_parse}")
                continue

            current_conversation_id = incoming_frame.conversation_id
            if not current_conversation_id:
                await send_websocket_message(websocket, "error", content=" 'conversation_id' 是必需的。")
                continue

            # 使用为WebSocket设计的辅助函数获取或创建Agent
            try:
                current_agent = await _get_or_create_agent_for_websocket(
                    current_conversation_id, 
                    ws_connection, 
                    incoming_frame.workflow_name
                )
            except Exception as e_agent:
                # 捕获Agent创建或获取时的异常，但保持WebSocket连接
                error_msg = f"获取或创建Agent失败 (conv_id: {current_conversation_id}): {e_agent}"
                logger.error(error_msg)
                await send_websocket_message(
                    websocket, 
                    "error", 
                    content=f"Agent初始化错误: {str(e_agent)}服务将继续尝试连接。", 
                    conv_id=current_conversation_id
                )
                # 延迟一下再继续，避免无限错误循环
                await asyncio.sleep(2)
                continue
            
            msg_type = incoming_frame.type
            
            if msg_type == "user_input":
                if incoming_frame.content is not None:
                    # 通过WebSocket的用户输入仍然通过agent的通用消息处理器处理
                    try:
                        await current_agent.handle_incoming_message({
                            "type": "user_input",
                            "conversation_id": current_conversation_id,
                            "content": incoming_frame.content,
                            "stream_mode": incoming_frame.stream_mode or False
                        })
                    except Exception as e_handle:
                        logger.error(f"处理用户输入消息出错: {e_handle}", show_traceback=True)
                        await send_websocket_message(
                            websocket, 
                            "error", 
                            content=f"处理消息时出错: {str(e_handle)}", 
                            conv_id=current_conversation_id
                        )
                else:
                    await send_websocket_message(websocket, "error", content="用户输入需要提供'content'字段", conv_id=current_conversation_id)
            
            elif msg_type == "disconnect_request":
                logger.info(f"客户端请求断开连接，对话ID: {current_conversation_id}")
                if current_agent:
                    try:
                        await current_agent.handle_incoming_message({
                            "type": "user_disconnect", 
                            "conversation_id": current_conversation_id, 
                            "content": "客户端请求断开连接"
                        })
                    except Exception as e_disconnect:
                        logger.error(f"处理断开连接请求出错: {e_disconnect}", show_traceback=True)
                break

            elif msg_type == "request_initial_state":
                try:
                    await send_initial_state(current_agent, websocket, current_conversation_id)
                    
                    # 发送用户就绪消息，启动工作流直到第一个用户输入点
                    await current_agent.handle_incoming_message({
                        "type": "user_ready",
                        "conversation_id": current_conversation_id,
                        "stream_mode": incoming_frame.stream_mode or False
                    })
                except Exception as e_initial:
                    logger.error(f"发送初始状态时出错: {e_initial}", show_traceback=True)
                    await send_websocket_message(
                        websocket, 
                        "error", 
                        content=f"获取初始状态失败: {str(e_initial)}", 
                        conv_id=current_conversation_id
                    )
            
            elif msg_type == "request_conversation_history":
                try:
                    messages_list = await current_agent.conversation.history.get_messages()
                    api_messages_list = [_convert_message_to_api_message(msg) for msg in messages_list]
                    payload = ConversationHistoryResponse(history=api_messages_list).model_dump(exclude_none=True)
                    await send_websocket_message(websocket, "conversation_history_response", payload=payload, conv_id=current_conversation_id)
                except Exception as e_history:
                    logger.error(f"获取对话历史时出错: {e_history}", show_traceback=True)
                    await send_websocket_message(
                        websocket, 
                        "error", 
                        content=f"获取对话历史失败: {str(e_history)}", 
                        conv_id=current_conversation_id
                    )
            
            elif msg_type == "request_libraries_status":
                try:
                    payload = await current_agent.get_all_libraries_details()
                    await send_websocket_message(websocket, "libraries_status_response", payload=payload, conv_id=current_conversation_id)
                except Exception as e_libs:
                    logger.error(f"获取库状态时出错: {e_libs}", show_traceback=True)
                    await send_websocket_message(
                        websocket, 
                        "error", 
                        content=f"获取库状态失败: {str(e_libs)}", 
                        conv_id=current_conversation_id
                    )
            
            elif msg_type == "request_workflow_status":
                try:
                    workflow_payload = None
                    workflow_lib = next((lib for lib in current_agent.libraries.values() if isinstance(lib, WorkflowLibrary)), None)
                    if workflow_lib:
                        workflow_payload = await workflow_lib.get_library_details()
                    
                    await send_websocket_message(websocket, "workflow_status_response", 
                                               payload=workflow_payload or {"success": False, "error": "未找到WorkflowLibrary或无活动工作流"},
                                               conv_id=current_conversation_id)
                except Exception as e_workflow:
                    logger.error(f"获取工作流状态时出错: {e_workflow}", show_traceback=True)
                    await send_websocket_message(
                        websocket, 
                        "error", 
                        content=f"获取工作流状态失败: {str(e_workflow)}", 
                        conv_id=current_conversation_id
                    )
            else:
                logger.warning(f"WebSocket收到未知的消息类型: {msg_type}，对话ID: {current_conversation_id}")
                await send_websocket_message(websocket, "error", content=f"未知的消息类型: {msg_type}", conv_id=current_conversation_id)

    except WebSocketDisconnect:
        logger.info(f"WebSocket已断开，对话ID: {current_conversation_id or '[未知]'}")
        if current_agent and current_conversation_id:
            try:
                await current_agent.handle_incoming_message({
                    "type": "user_disconnect", 
                    "conversation_id": current_conversation_id, 
                    "content": "WebSocket连接已关闭/断开"
                })
            except Exception as e_disconnect:
                logger.error(f"处理WebSocket断开时出错: {e_disconnect}", show_traceback=True)
    except Exception as e_ws_loop:
        error_msg = f"WebSocket主循环出错，对话ID: {current_conversation_id or '[未知]'}: {e_ws_loop}"
        logger.error(error_msg, show_traceback=True)
        if websocket.client_state == websocket.client_state.CONNECTED: # 在发送前检查是否仍然连接
            await send_websocket_message(websocket, "error", content=f"服务器错误: {str(e_ws_loop)}", conv_id=current_conversation_id)
            
    finally:
        logger.info(f"正在关闭WebSocket连接处理程序，对话ID: {current_conversation_id or '[未知]'}")
        if websocket.client_state == websocket.client_state.CONNECTED:
            await websocket.close()

@app.websocket("/ws/v1/remote_workflow_connector")
async def remote_workflow_websocket_endpoint(websocket: WebSocket):
    """远程工作流 WebSocket 端点处理"""
    from libraries.workflow_server import get_connection_manager
    
    # 获取全局连接管理器并处理连接
    conn_manager = get_connection_manager()
    await conn_manager.handle_websocket_connection(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000) 
