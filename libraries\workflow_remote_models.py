"""
远程工作流通信协议的数据模型。

该模块定义了 WorkflowClient 和 WorkflowServer 之间通信所需的所有数据结构。
"""

from typing import Any, Optional, List, Dict, Union
from pydantic import BaseModel, Field
from libraries.workflow_models import ActionDefinition, CaseDefinition


class RemoteStepDefinition(BaseModel):
    """远程步骤定义，用于客户端向服务器发送步骤信息"""
    operation: str = Field(..., description="步骤操作类型：EXECUTE, CONDITION, GENERATE, USER_INPUT, SWITCH, HALT")
    name: Optional[str] = Field(None, description="步骤名称")
    description: Optional[str] = Field(None, description="步骤描述")
    
    # EXECUTE 步骤专用字段
    actions: Optional[List[ActionDefinition]] = Field(None, description="执行动作列表")
    
    # CONDITION 步骤专用字段
    condition_description: Optional[str] = Field(None, description="条件描述")
    
    # GENERATE 步骤专用字段
    content_description: Optional[str] = Field(None, description="生成内容描述")
    wait_user: Optional[bool] = Field(False, description="生成后是否等待用户输入")
    
    # SWITCH 步骤专用字段
    cases: Optional[List[CaseDefinition]] = Field(None, description="分支选项列表")


class WorkflowClientMessage(BaseModel):
    """客户端发送给服务器的消息"""
    type: str = Field(..., description="消息类型")
    workflow_instance_id: Optional[str] = Field(None, description="工作流实例ID")
    client_request_id: Optional[str] = Field(None, description="客户端请求ID")
    workflow_script_name: Optional[str] = Field(None, description="工作流脚本名称")
    step_definition: Optional[RemoteStepDefinition] = Field(None, description="步骤定义")


class WorkflowServerMessage(BaseModel):
    """服务器发送给客户端的消息"""
    type: str = Field(..., description="消息类型")
    workflow_instance_id: Optional[str] = Field(None, description="工作流实例ID")
    client_request_id: Optional[str] = Field(None, description="客户端请求ID")
    result: Optional[Any] = Field(None, description="步骤执行结果")
    error: Optional[str] = Field(None, description="错误信息")


# 消息类型常量
class MessageType:
    # 客户端发送的消息类型
    START_WORKFLOW_INSTANCE = "start_workflow_instance"
    DEFINE_STEP = "define_step"
    
    # 服务器发送的消息类型
    WORKFLOW_INSTANCE_STARTED = "workflow_instance_started"
    REQUEST_NEXT_STEP_DEFINITION = "request_next_step_definition"
    READY_FOR_NEXT_STEP = "ready_for_next_step"
    STEP_RESULT = "step_result"
    WORKFLOW_COMPLETED = "workflow_completed"
    ERROR = "error"


