"""
MeowAgent 客户端 API 服务器 (client_api.py)

该模块提供专门用于远程工作流功能的 API 服务器。
它独立于主 API 服务器运行，专注于处理远程工作流客户端的连接和通信。

主要功能：
1. 远程工作流 WebSocket 连接管理
2. 动态工作流创建和 Agent 启动
3. 消息路由和协议处理
4. 连接生命周期管理

使用方法：
python client_api.py --port 8001
"""

import asyncio
import json
import traceback
import uuid
import argparse
from datetime import datetime
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from agent import Agent
from log import logger
from agent_manager import AgentManager

# 导入基础库类
from libraries.library import Library
from libraries.workflow import WorkflowLibrary
from libraries.workflow_models import UserInputStep
from model import OpenAIModel
import config
from connection import Connection
from libraries.mcp_client import MCPClientLibrary


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("客户端 API 服务器启动中...")

    # 初始化 AgentManager
    agent_manager = await AgentManager.get_instance()
    logger.info("AgentManager 已初始化")

    yield

    logger.info("客户端 API 服务器关闭中...")
    await agent_manager.close()
    logger.info("AgentManager 已关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="MeowAgent 客户端 API",
    description="专门用于远程工作流功能的 API 服务器",
    version="1.0.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应配置为特定的前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# API 数据模型
class RemoteWorkflowStatus(BaseModel):
    """远程工作流状态响应模型"""
    active_connections: int
    registered_workflows: List[str]
    active_instances: List[str]


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    timestamp: str
    version: str


# 健康检查端点
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0"
    )


# 远程工作流状态端点
@app.get("/api/v1/remote_workflow/status", response_model=RemoteWorkflowStatus)
async def get_remote_workflow_status():
    """获取远程工作流服务状态"""
    try:
        from libraries.workflow_server import RemoteWorkflowConnectionManager
        conn_manager = await RemoteWorkflowConnectionManager.get_instance()

        return RemoteWorkflowStatus(
            active_connections=len(conn_manager.connections),
            registered_workflows=list(conn_manager.registered_remote_workflows),
            active_instances=list(conn_manager.connections.keys())
        )
    except Exception as e:
        logger.error(f"获取远程工作流状态失败: {e}")
        return RemoteWorkflowStatus(
            active_connections=0,
            registered_workflows=[],
            active_instances=[]
        )


@app.websocket("/ws/v1/remote_workflow_connector")
async def remote_workflow_websocket_endpoint(websocket: WebSocket):
    """远程工作流连接器的 WebSocket 端点"""
    await websocket.accept()

    # 获取连接管理器
    from libraries.workflow_server import RemoteWorkflowConnectionManager
    conn_manager = await RemoteWorkflowConnectionManager.get_instance()

    current_workflow_instance_ids = []

    try:
        logger.info("远程工作流客户端已连接")

        while True:
            # 接收消息
            raw_message = await websocket.receive_text()
            logger.debug(f"收到远程工作流消息: {raw_message}")

            # 处理消息
            workflow_instance_id = await conn_manager.handle_websocket_message(websocket, raw_message)

            # 如果是启动工作流实例的消息，记录实例 ID 用于清理
            if workflow_instance_id:
                current_workflow_instance_ids.append(workflow_instance_id)
                try:
                    # 动态创建远程工作流并启动 Agent
                    await start_remote_workflow_agent(workflow_instance_id, conn_manager)
                    logger.info(f"远程工作流实例已启动: {workflow_instance_id}")
                except Exception as e:
                    logger.error(f"启动远程工作流实例失败: {e}")
                    logger.debug(traceback.format_exc())
                    # 发送错误消息给客户端
                    try:
                        from libraries.workflow_remote_models import WorkflowServerMessage, MessageType
                        error_response = WorkflowServerMessage(
                            type=MessageType.ERROR,
                            workflow_instance_id=workflow_instance_id,
                            error=f"启动工作流实例失败: {str(e)}"
                        )
                        await websocket.send_text(error_response.model_dump_json())
                    except Exception as send_error:
                        logger.error(f"发送错误响应失败: {send_error}")

    except Exception as e:
        logger.error(f"远程工作流 WebSocket 连接出错: {e}")
        logger.debug(traceback.format_exc())
    finally:
        # 清理所有相关的工作流实例
        for instance_id in current_workflow_instance_ids:
            try:
                conn_manager.cleanup_connection(instance_id)
                logger.info(f"已清理远程工作流实例: {instance_id}")
            except Exception as e:
                logger.error(f"清理远程工作流实例失败 {instance_id}: {e}")

        logger.info("远程工作流客户端连接已关闭")


async def start_remote_workflow_agent(workflow_instance_id: str, conn_manager):
    """
    为远程工作流实例启动 Agent

    参数:
        workflow_instance_id: 远程工作流实例 ID
        conn_manager: 远程工作流连接管理器
    """
    try:
        # 获取 AgentManager 实例
        agent_manager = await AgentManager.get_instance()

        # 从实例ID中提取脚本名称，或使用默认值
        # 这里我们可以从连接管理器中获取脚本名称，或者使用一个通用的名称
        script_name = f"remote_workflow_{workflow_instance_id}"

        # 动态创建远程工作流源
        from libraries.workflow_server import create_remote_workflow_for_agent

        # 创建 WorkflowSource 对象，类型为 'remote'
        workflow_source = create_remote_workflow_for_agent(script_name, workflow_instance_id)

        # 检查是否已有一个默认的 Agent 实例可以使用
        # 或者创建一个新的 Agent 来运行远程工作流
        
        # 获取第一个可用的 Agent 或创建新的（简化处理）
        available_agents = list(agent_manager.agents.values())
        if available_agents:
            # 使用现有的第一个 Agent
            agent = available_agents[0]
            logger.info(f"使用现有 Agent {agent.conversation_id} 运行远程工作流")
        else:
            # 创建一个临时的对话和 Agent
            from conversation import Conversation
            temp_conversation = Conversation(f"remote_workflow_{workflow_instance_id}")
            agent = await agent_manager.create_agent(
                conversation_id=temp_conversation.conversation_id,
                conversation=temp_conversation,
                output_connection=None  # 远程工作流不需要输出连接
            )
            logger.info(f"创建新的临时 Agent {agent.conversation_id} 运行远程工作流")

        # 获取工作流库
        workflow_library = None
        for lib in agent.libraries.values():
            if isinstance(lib, WorkflowLibrary):
                workflow_library = lib
                break

        if not workflow_library:
            raise RuntimeError("找不到工作流库实例")

        # 动态添加到工作流库
        workflow_library.workflows[script_name] = workflow_source
        logger.info(f"已将远程工作流 {script_name} 添加到工作流库")

        # 设置并启动远程工作流
        result = await workflow_library.set_current_workflow(script_name)
        if not result.get("success", False):
            raise RuntimeError(f"设置远程工作流失败: {result.get('error', '未知错误')}")

        # 点燃远程工作流生成器 - 使用异步任务避免阻塞
        ignite_message = {
            "type": "system_command",
            "content": "IGNITE_REMOTE_WORKFLOW",
            "conversation_id": agent.conversation_id
        }
        # 创建异步任务来点燃工作流，不等待其完成
        asyncio.create_task(agent.handle_incoming_message(ignite_message))

        logger.info(f"远程工作流 Agent 已启动: {workflow_instance_id} -> {script_name}")

    except Exception as e:
        logger.error(f"启动远程工作流 Agent 失败: {e}")
        logger.debug(traceback.format_exc())
        raise



def main():
    """主函数，启动客户端 API 服务器"""
    parser = argparse.ArgumentParser(description="MeowAgent 客户端 API 服务器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用自动重载（开发模式）")

    args = parser.parse_args()

    logger.info(f"启动客户端 API 服务器: {args.host}:{args.port}")

    try:
        import uvicorn
        uvicorn.run(
            "client_api:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level="info"
        )
    except ImportError:
        logger.error("uvicorn 未安装，请运行: pip install uvicorn")
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")


if __name__ == "__main__":
    main()
