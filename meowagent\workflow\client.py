"""
WorkflowClient SDK - 远程工作流客户端。

该模块提供 WorkflowClient 类，允许外部 Python 脚本通过 WebSocket
连接到 MeowAgent 服务器，远程定义和驱动工作流执行。

WorkflowClient 提供与 WorkflowContext 完全相同的接口，
使得远程工作流的编写和本地工作流完全一致。
"""

import asyncio
import json
import uuid
import websockets
from typing import Dict, Any, List, Optional, Callable, Awaitable, Union
from libraries.workflow_models import ActionDefinition, CaseDefinition, Case
from libraries.workflow_remote_models import (
    WorkflowClientMessage, WorkflowServerMessage, RemoteStepDefinition, MessageType
)
from message import UserMessage
from log import logger

TIMEOUT = 3600

class WorkflowSession:
    """工作流会话，代表一个正在执行的远程工作流实例"""
    
    def __init__(self, client: 'WorkflowClient', instance_id: str):
        self._client = client
        self.instance_id = instance_id
    
    async def execute(
        self, 
        description: str, 
        actions: List[ActionDefinition],
        name: Optional[str] = None
    ) -> None:
        """
        执行一个 EXECUTE 步骤。
        
        参数:
            description: 步骤描述
            actions: 要执行的动作定义列表
            name: 可选的步骤名称
        """
        step_def = RemoteStepDefinition(
            operation="EXECUTE",
            name=name,
            description=description,
            actions=actions
        )
        await self._client._send_step_and_wait(self.instance_id, step_def)
        
    async def condition(
        self,
        description: str,
        name: Optional[str] = None
    ) -> bool:
        """
        执行一个 CONDITION 步骤并返回条件判断结果。
        
        参数:
            description: 条件描述
            name: 可选的步骤名称
            
        返回:
            条件判断的结果 (True/False)
        """
        step_def = RemoteStepDefinition(
            operation="CONDITION",
            name=name,
            description=description,
            condition_description=description
        )
        result = await self._client._send_step_and_wait_for_result(self.instance_id, step_def)
        return bool(result)
        
    async def generate(
        self,
        description: str,
        wait_user: bool = False,
        name: Optional[str] = None
    ) -> None:
        """
        执行一个 GENERATE 步骤。
        
        参数:
            description: 生成内容的描述
            wait_user: 是否在生成后等待用户输入
            name: 可选的步骤名称
        """
        step_def = RemoteStepDefinition(
            operation="GENERATE",
            name=name,
            description=description,
            content_description=description,
            wait_user=wait_user
        )
        await self._client._send_step_and_wait(self.instance_id, step_def)
        
    async def user_input(
        self,
        description: str,
        name: Optional[str] = None
    ) -> UserMessage:
        """
        等待用户输入并返回用户消息。
        
        参数:
            description: 等待用户输入的提示描述
            name: 可选的步骤名称
            
        返回:
            用户输入的消息对象
        """
        step_def = RemoteStepDefinition(
            operation="USER_INPUT",
            name=name,
            description=description
        )
        result = await self._client._send_step_and_wait_for_result(self.instance_id, step_def)
        if isinstance(result, dict) and 'content' in result:
            return UserMessage(content=result['content'])
        elif isinstance(result, UserMessage):
            return result
        else:
            logger.warning(f"用户输入步骤返回了非预期类型: {type(result)}")
            return UserMessage(content=str(result) if result is not None else "")
        
    async def switch(
        self,
        name: str,
        description: str,
        cases: List[Union[CaseDefinition, Case, Dict[str, Any]]],
        default_handler: Optional[Callable[['WorkflowSession', str], Awaitable[None]]] = None
    ) -> str:
        """
        执行一个 SWITCH 步骤，允许根据 LLM 的选择切换到不同的工作流分支。
        
        参数:
            name: 步骤名称
            description: 步骤描述
            cases: 可选择的分支列表，可以是 CaseDefinition、Case 或字典
            default_handler: 如果选择的分支没有对应的处理函数时，执行的默认处理函数
            
        返回:
            被选择的分支名称
        """
        # 构建用于 LLM 的 CaseDefinition 对象和处理函数映射
        processed_cases = []
        case_to_handler_map = {}
        
        for case in cases:
            if isinstance(case, Case):
                # 如果是 Case 对象，提取处理函数并创建 CaseDefinition
                processed_cases.append(CaseDefinition(
                    name=case.name,
                    description=case.description
                ))
                case_to_handler_map[case.name] = case.handler
            elif isinstance(case, dict):
                # 兼容字典格式
                case_name = case.get('name')
                case_desc = case.get('description', '')
                case_handler = case.get('handler')
                
                if case_handler:
                    case_to_handler_map[case_name] = case_handler
                
                processed_cases.append(CaseDefinition(
                    name=case_name,
                    description=case_desc
                ))
            else:
                # 如果是 CaseDefinition 对象，直接添加
                processed_cases.append(case)
        
        # 创建并发送 SWITCH 步骤
        step_def = RemoteStepDefinition(
            operation="SWITCH",
            name=name,
            description=description,
            cases=processed_cases
        )
        selected_branch = await self._client._send_step_and_wait_for_result(self.instance_id, step_def)
        selected_branch = str(selected_branch) if selected_branch is not None else ""
        
        # 执行选中分支的处理函数
        handler = case_to_handler_map.get(selected_branch)
        if handler:
            logger.debug(f"执行分支 '{selected_branch}' 的处理函数")
            await handler(self)
        elif default_handler:
            logger.debug(f"执行默认处理函数，分支名称: '{selected_branch}'")
            await default_handler(self, selected_branch)
        else:
            logger.debug(f"没有找到分支 '{selected_branch}' 的处理函数，返回分支名称")
        
        # 返回选择的分支名称，以保持兼容性
        return selected_branch
        
    async def halt(
        self,
        name: Optional[str] = None
    ) -> None:
        """
        终止工作流执行。
        
        参数:
            name: 可选的步骤名称
        """
        step_def = RemoteStepDefinition(
            operation="HALT",
            name=name,
            description="工作流终止"
        )
        await self._client._send_step_and_wait(self.instance_id, step_def)


class WorkflowClient:
    """WorkflowClient - 远程工作流客户端"""
    
    def __init__(self):
        self._websocket: Optional[websockets.WebSocketServerProtocol] = None
        self._pending_requests: Dict[str, asyncio.Future] = {}
        self._ready_signal_queues: Dict[str, asyncio.Queue] = {}  # 修改：使用 Queue 替代 Future
        self._receive_task: Optional[asyncio.Task] = None
        self._connected = False
    
    async def connect(self, server_url: str) -> None:
        """
        连接到 MeowAgent 工作流服务器。
        
        参数:
            server_url: 服务器 WebSocket URL，例如 "ws://localhost:8001/ws/v1/remote_workflow_connector"
        """
        try:
            self._websocket = await websockets.connect(server_url, ping_timeout=TIMEOUT)
            self._connected = True
            # 启动接收消息的任务
            self._receive_task = asyncio.create_task(self._receive_messages())
            logger.info(f"已连接到工作流服务器: {server_url}")
        except Exception as e:
            logger.error(f"连接到工作流服务器失败: {e}")
            raise
    
    async def disconnect(self) -> None:
        """断开与服务器的连接"""
        self._connected = False
        
        # 取消接收任务
        if self._receive_task and not self._receive_task.done():
            self._receive_task.cancel()
            try:
                await self._receive_task
            except asyncio.CancelledError:
                pass
        
        # 关闭 WebSocket 连接
        if self._websocket:
            await self._websocket.close()
            self._websocket = None
        
        # 取消所有等待中的请求
        for future in self._pending_requests.values():
            if not future.done():
                future.cancel()
        self._pending_requests.clear()
        
        # 清理准备好信号的队列字典
        # 对于队列，当等待它们的任务被取消时，它们会自动处理。
        # 我们只需要清空字典即可。
        self._ready_signal_queues.clear()
        
        logger.info("已断开与工作流服务器的连接")
    
    # 添加 close 方法作为 disconnect 的别名
    async def close(self) -> None:
        """关闭连接（disconnect 的别名）"""
        await self.disconnect()
    
    async def start_workflow_instance(self, workflow_script_name: str) -> WorkflowSession:
        """
        启动一个新的工作流实例。
        
        参数:
            workflow_script_name: 要启动的工作流脚本名称
            
        返回:
            WorkflowSession 对象，可用于定义工作流步骤
        """
        if not self._connected or not self._websocket:
            raise RuntimeError("尚未连接到服务器，请先调用 connect()")
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 创建消息
        message = WorkflowClientMessage(
            type=MessageType.START_WORKFLOW_INSTANCE,
            workflow_script_name=workflow_script_name,
            client_request_id=request_id
        )
        
        # 设置等待的 Future
        future = asyncio.Future()
        self._pending_requests[request_id] = future
        
        # 发送消息
        message_json = message.model_dump_json()
        logger.debug(f"发送消息: {message_json}")
        await self._websocket.send(message_json)
        
        try:
            # 等待响应
            response = await asyncio.wait_for(future, timeout=TIMEOUT)
            if response.type == MessageType.WORKFLOW_INSTANCE_STARTED:
                instance_id = response.workflow_instance_id
                logger.info(f"工作流实例已启动: {instance_id}")
                
                # 等待服务器发送准备好信号
                logger.debug(f"等待服务器准备好信号 (实例 {instance_id})")
                if instance_id not in self._ready_signal_queues:
                    # 按需创建队列，尽管通常应该由 _receive_messages 创建
                    self._ready_signal_queues[instance_id] = asyncio.Queue()
                    logger.debug(f"在 start_workflow_instance 中为实例 {instance_id} 创建了 ready_signal_queue")
                
                ready_queue = self._ready_signal_queues[instance_id]
                
                try:
                    await asyncio.wait_for(ready_queue.get(), timeout=TIMEOUT)
                    logger.info(f"服务器已准备好接收步骤 (实例 {instance_id})")
                    # 标记队列中的一项已被消耗
                    ready_queue.task_done() # 虽然我们不 join 队列，但这是良好实践
                    return WorkflowSession(self, instance_id)
                except asyncio.TimeoutError:
                    # 超时发生时，队列可能仍存在于字典中，但下次 get 会重新等待
                    # 如果需要，可以在这里 self._ready_signal_queues.pop(instance_id, None)
                    raise RuntimeError(f"等待服务器准备好信号超时 (实例 {instance_id})")
                    
            elif response.type == MessageType.ERROR:
                raise RuntimeError(f"启动工作流实例失败: {response.error}")
            else:
                raise RuntimeError(f"收到意外的响应类型: {response.type}")
        except asyncio.TimeoutError:
            self._pending_requests.pop(request_id, None)
            raise RuntimeError("启动工作流实例超时")
    
    async def _send_step_and_wait(self, instance_id: str, step_def: RemoteStepDefinition) -> None:
        """发送步骤定义并等待完成信号"""
        await self._send_step_and_wait_for_result(instance_id, step_def)
    
    async def _send_step_and_wait_for_result(self, instance_id: str, step_def: RemoteStepDefinition) -> Any:
        """发送步骤定义并等待具体的结果值"""
        if not self._connected or not self._websocket:
            raise RuntimeError("WebSocket 连接已断开")
        
        request_id = str(uuid.uuid4())
        message = WorkflowClientMessage(
            type=MessageType.DEFINE_STEP,
            workflow_instance_id=instance_id,
            client_request_id=request_id,
            step_definition=step_def
        )
        
        # 设置等待的 Future
        future = asyncio.Future()
        self._pending_requests[request_id] = future
        
        # 发送消息
        await self._websocket.send(message.model_dump_json())
        
        try:
            # 等待响应
            response = await asyncio.wait_for(future, timeout=TIMEOUT)  
            if response.type == MessageType.STEP_RESULT:
                # 步骤执行完成后，等待服务器发送下一个准备好信号（除非是HALT步骤）
                if step_def.operation != "HALT":
                    logger.debug(f"等待服务器下一个准备好信号 (实例 {instance_id})")
                    if instance_id not in self._ready_signal_queues:
                        # 通常队列应该已存在
                        self._ready_signal_queues[instance_id] = asyncio.Queue()
                        logger.warning(f"在 _send_step_and_wait_for_result 中为实例 {instance_id} 创建了新的 ready_signal_queue")
                    
                    ready_queue = self._ready_signal_queues[instance_id]
                    try:
                        await asyncio.wait_for(ready_queue.get(), timeout=TIMEOUT)
                        logger.debug(f"服务器已准备好接收下一个步骤 (实例 {instance_id})")
                        ready_queue.task_done() # 良好实践
                    except asyncio.TimeoutError:
                        # 超时发生时，队列可能仍存在于字典中
                        logger.warning(f"等待服务器下一个准备好信号超时 (实例 {instance_id})")
                        # 这里不抛出异常，允许继续进行，但后续步骤可能会因为没有及时收到ready信号而出错
                        # 或者也可以选择在这里抛出异常，取决于期望的行为
                
                return response.result
            elif response.type == MessageType.ERROR:
                raise RuntimeError(f"步骤执行错误: {response.error}")
            else:
                raise RuntimeError(f"收到意外的响应类型: {response.type}")
        except asyncio.TimeoutError:
            self._pending_requests.pop(request_id, None)
            raise RuntimeError("步骤执行超时")
    
    async def _receive_messages(self) -> None:
        """接收来自服务器的消息"""
        try:
            while self._connected and self._websocket:
                try:
                    raw_message = await self._websocket.recv()
                    logger.debug(f"收到服务器消息: {raw_message}")
                    
                    message_data = json.loads(raw_message)
                    response = WorkflowServerMessage(**message_data)
                    
                    # 处理准备好信号
                    if response.type == MessageType.READY_FOR_NEXT_STEP:
                        instance_id = response.workflow_instance_id
                        if instance_id:
                            if instance_id not in self._ready_signal_queues:
                                # 如果队列不存在，可能是第一次收到此实例的信号，或者之前的被清理
                                self._ready_signal_queues[instance_id] = asyncio.Queue()
                                logger.debug(f"为实例 {instance_id} 创建了新的 ready_signal_queue")
                            try:
                                self._ready_signal_queues[instance_id].put_nowait(True) # 发送信号
                                logger.debug(f"已将 READY_FOR_NEXT_STEP 信号放入队列: {instance_id}")
                            except asyncio.QueueFull:
                                # 理论上不应该发生，因为我们使用的是无界队列
                                logger.warning(f"尝试向已满的 ready_signal_queue 添加信号失败: {instance_id}")
                        else:
                            logger.warning(f"收到缺少 instance_id 的 READY_FOR_NEXT_STEP 信号")
                        continue
                    
                    # 如果是 REQUEST_NEXT_STEP_DEFINITION 消息，目前暂时忽略
                    # 因为在这个简化实现中，客户端主动发送步骤定义
                    if response.type == MessageType.REQUEST_NEXT_STEP_DEFINITION:
                        logger.debug(f"收到服务器请求下一步定义 (实例 {response.workflow_instance_id})")
                        continue
                    
                    # 处理有 client_request_id 的响应
                    if response.client_request_id and response.client_request_id in self._pending_requests:
                        future = self._pending_requests.pop(response.client_request_id)
                        if not future.done():
                            future.set_result(response)
                        logger.debug(f"已处理请求响应: {response.client_request_id}")
                    else:
                        logger.warning(f"收到无法匹配的服务器消息: {response.type}, request_id: {response.client_request_id}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"解析服务器消息失败: {e}, 原始消息: {raw_message}")
                except Exception as e:
                    logger.error(f"处理服务器消息时出错: {e}")
                    
        except Exception as e:
            logger.error(f"接收消息任务出错: {e}")
        finally:
            self._connected = False
            logger.debug("消息接收任务已结束") 